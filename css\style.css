/* Enhanced Modern UI Styles - Logic.io Redesign */

/* Additional modern enhancements */
.custom-block {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 16px;
    overflow: hidden;
}

.custom-block:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Enhanced button styles */
.btn {
    border-radius: 12px;
    font-weight: 600;
    letter-spacing: 0.5px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

/* Modern section styling */
.section-padding {
    padding: 80px 0;
}

/* Enhanced hero section */
.hero-section {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

/* Modern timeline styling */
.timeline-container {
    position: relative;
}

.vertical-scrollable-timeline li {
    transition: all 0.3s ease;
}

.vertical-scrollable-timeline li:hover {
    transform: translateX(10px);
}

/* Enhanced form styling */
.form-control {
    border-radius: 12px;
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
    padding: 12px 16px;
}

.form-control:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    transform: translateY(-1px);
}

/* Modern card hover effects */
.services-carousel .custom-block {
    border: 1px solid rgba(59, 130, 246, 0.1);
    backdrop-filter: blur(10px);
}

.services-carousel .custom-block:hover {
    border-color: rgba(59, 130, 246, 0.3);
    background: rgba(255, 255, 255, 0.95);
}

/* Smooth page transitions */
html {
    scroll-behavior: smooth;
}

/* Loading animation for better UX */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Enhanced mobile experience */
@media (max-width: 768px) {
    .section-padding {
        padding: 60px 0;
    }

    .custom-block {
        margin-bottom: 20px;
    }

    .btn {
        padding: 12px 24px;
        font-size: 14px;
    }
}

/* Accessibility improvements */
.nav-link:focus,
.btn:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Reduced motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
