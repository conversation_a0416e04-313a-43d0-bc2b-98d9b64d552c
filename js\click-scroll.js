/**
 * Enhanced Click-Scroll Navigation
 * Modern, performant navigation with smooth animations
 * Redesigned for better UX and performance
 */

(function ($) {
    'use strict';

    // Configuration
    const config = {
        sections: [1, 2, 3, 4, 5],
        offset: 75,
        scrollDuration: 600,
        throttleDelay: 16, // ~60fps
        easing: 'easeInOutCubic'
    };

    // Performance optimization: throttle scroll events
    let isScrolling = false;

    // Cache DOM elements for better performance
    const $navLinks = $('.navbar-nav .nav-item .nav-link');
    const $clickScrollLinks = $('.click-scroll');

    // Custom easing function for smoother animations
    $.easing.easeInOutCubic = function (_, t, b, c, d) {
        if ((t /= d / 2) < 1) return c / 2 * t * t * t + b;
        return c / 2 * ((t -= 2) * t * t + 2) + b;
    };

    // Enhanced scroll handler with throttling
    function handleScroll() {
        if (isScrolling) return;

        isScrolling = true;
        requestAnimationFrame(() => {
            const docScroll = $(document).scrollTop();
            let activeIndex = -1;

            // Find the current active section
            config.sections.forEach((value, index) => {
                const $section = $('#section_' + value);
                if ($section.length) {
                    const offsetSection = $section.offset().top - config.offset;
                    if (docScroll >= offsetSection) {
                        activeIndex = index;
                    }
                }
            });

            // Update navigation states with smooth transitions
            updateNavigation(activeIndex);
            isScrolling = false;
        });
    }

    // Update navigation with enhanced visual feedback
    function updateNavigation(activeIndex) {
        $navLinks.removeClass('active').addClass('inactive');

        if (activeIndex >= 0) {
            const $activeLink = $navLinks.eq(activeIndex);
            $activeLink.addClass('active').removeClass('inactive');

            // Add subtle pulse effect for active item
            $activeLink.addClass('pulse-effect');
            setTimeout(() => $activeLink.removeClass('pulse-effect'), 300);
        }
    }

    // Enhanced click handler with smooth scrolling
    function setupClickHandlers() {
        config.sections.forEach((value, index) => {
            $clickScrollLinks.eq(index).on('click', function (e) {
                e.preventDefault();

                const $section = $('#section_' + value);
                if ($section.length) {
                    const offsetClick = $section.offset().top - config.offset;

                    // Smooth scroll with custom easing
                    $('html, body').animate({
                        scrollTop: offsetClick
                    }, {
                        duration: config.scrollDuration,
                        easing: config.easing,
                        complete: function () {
                            // Update URL hash without jumping
                            if (history.pushState) {
                                history.pushState(null, null, '#section_' + value);
                            }
                        }
                    });
                }
            });
        });
    }

    // jQuery throttle function (lightweight implementation)
    $.throttle = function (delay, fn) {
        let lastCall = 0;
        return function (...args) {
            const now = Date.now();
            if (now - lastCall >= delay) {
                lastCall = now;
                return fn.apply(this, args);
            }
        };
    };

    // Initialize navigation
    function initNavigation() {
        // Set initial states
        $navLinks.addClass('inactive');
        $navLinks.eq(0).addClass('active').removeClass('inactive');

        // Setup event handlers
        $(document).on('scroll', $.throttle(config.throttleDelay, handleScroll));
        setupClickHandlers();

        // Handle initial page load with hash
        const hash = window.location.hash;
        if (hash) {
            setTimeout(() => {
                const $target = $(hash);
                if ($target.length) {
                    const offsetClick = $target.offset().top - config.offset;
                    $('html, body').scrollTop(offsetClick);
                }
            }, 100);
        }
    }

    // Initialize when document is ready
    $(document).ready(initNavigation);

})(jQuery);