(function ($) {
    'use strict';

    const config = {
        sections: [1, 2, 3, 4, 5],
        offset: 75,
        scrollDuration: 600,
        throttleDelay: 16,
        easing: 'easeInOutCubic'
    };

    let isScrolling = false;

    const $navLinks = $('.navbar-nav .nav-item .nav-link');
    const $clickScrollLinks = $('.click-scroll');

    $.easing.easeInOutCubic = function (_, t, b, c, d) {
        if ((t /= d / 2) < 1) return c / 2 * t * t * t + b;
        return c / 2 * ((t -= 2) * t * t + 2) + b;
    };

    function handleScroll() {
        if (isScrolling) return;

        isScrolling = true;
        requestAnimationFrame(() => {
            const docScroll = $(document).scrollTop();
            let activeIndex = -1;

            config.sections.forEach((value, index) => {
                const $section = $('#section_' + value);
                if ($section.length) {
                    const offsetSection = $section.offset().top - config.offset;
                    if (docScroll >= offsetSection) {
                        activeIndex = index;
                    }
                }
            });

            updateNavigation(activeIndex);
            isScrolling = false;
        });
    }

    function updateNavigation(activeIndex) {
        $navLinks.removeClass('active').addClass('inactive');

        if (activeIndex >= 0) {
            const $activeLink = $navLinks.eq(activeIndex);
            $activeLink.addClass('active').removeClass('inactive');

            $activeLink.addClass('pulse-effect');
            setTimeout(() => $activeLink.removeClass('pulse-effect'), 300);
        }
    }

    function setupClickHandlers() {
        config.sections.forEach((value, index) => {
            $clickScrollLinks.eq(index).on('click', function (e) {
                e.preventDefault();

                const $section = $('#section_' + value);
                if ($section.length) {
                    const offsetClick = $section.offset().top - config.offset;

                    $('html, body').animate({
                        scrollTop: offsetClick
                    }, {
                        duration: config.scrollDuration,
                        easing: config.easing,
                        complete: function () {
                            if (history.pushState) {
                                history.pushState(null, null, '#section_' + value);
                            }
                        }
                    });
                }
            });
        });
    }

    $.throttle = function (delay, fn) {
        let lastCall = 0;
        return function (...args) {
            const now = Date.now();
            if (now - lastCall >= delay) {
                lastCall = now;
                return fn.apply(this, args);
            }
        };
    };

    function initNavigation() {
        $navLinks.addClass('inactive');
        $navLinks.eq(0).addClass('active').removeClass('inactive');

        $(document).on('scroll', $.throttle(config.throttleDelay, handleScroll));
        setupClickHandlers();

        const hash = window.location.hash;
        if (hash) {
            setTimeout(() => {
                const $target = $(hash);
                if ($target.length) {
                    const offsetClick = $target.offset().top - config.offset;
                    $('html, body').scrollTop(offsetClick);
                }
            }, 100);
        }
    }

    $(document).ready(initNavigation);

})(jQuery);